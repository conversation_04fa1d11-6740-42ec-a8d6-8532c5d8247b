{"folders": [{"path": "."}], "settings": {"python.defaultInterpreterPath": "uv run python", "python.terminal.activateEnvironment": false, "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/*.pyo": true, "**/venv": true, "**/.venv": true, "**/*.egg-info": true, "**/.git": true}, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.tabSize": 4, "editor.insertSpaces": true}, "extensions": {"recommendations": ["ms-python.python", "ms-python.flake8", "ms-python.black-formatter"]}}