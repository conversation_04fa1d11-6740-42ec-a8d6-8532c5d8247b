# AI Systems - Instruction Sequence Executor

Multi-step LLM instruction sequence executor with template management system. Execute user prompts through sequences of different system instructions using multiple LLM models for diverse perspectives and step-by-step refinement.

## Features

- Execute prompts through multiple system instructions
- Support for multiple LLM models per step
- Template-based instruction sequences
- Cost tracking and structured JSON output
- Chain mode for sequential processing
- Aggregator sequences for result synthesis

## Quick Start

```bash
# Install dependencies
uv sync

# Run with default sequence
uv run python src/main.py --prompt "Your question here"

# Use specific sequence and models
uv run python src/main.py --sequence "0121" --models "gpt-4o,claude-3-haiku" --prompt "Your question"
```

## Core Components

- **main.py**: Core execution engine with LLM interaction via litellm
- **templates/**: Instruction template management system with stage-based organization
- **Template Catalog**: Dynamic template discovery and sequence management
- **Chain Mode**: Sequential step processing with output chaining
- **Aggregator**: Result synthesis across multiple steps
- **Cost Tracking**: Integrated cost calculation for API usage
- **Streaming Output**: Asynchronous execution with structured JSON results

## Usage Examples

```bash
# Basic usage with default sequence
uv run python src/main.py --prompt "Analyze this text for key insights"

# Specific sequence and multiple models
uv run python src/main.py --sequence "3031" --models "gpt-4o,claude-3-sonnet" --prompt "Your text here"

# Chain mode with aggregation
uv run python src/main.py --sequence "3100:a-c" --chain-mode --aggregator "3022" --prompt "Complex analysis task"

# Using embedded sequence and model specifications
uv run python src/main.py --prompt "[MODEL:gpt-4o|claude-3-haiku] [SEQ:3031|3100:a-c] Transform this text"
```

## Configuration

Set environment variables for API access:
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `OPENROUTER_API_KEY`

---

## 🎯 **Universal Template-Based Instruction Processing**

A revolutionary system that transforms any instruction or prompt into a standardized, machine-parsable, three-part canonical structure that can be executed in sequences and chained together.

### **Core Innovation**
- **Universal directive system** with syntactic transformation focus
- **Stage-based template organization** with automatic ID generation
- **Sequence execution engine** with type-safe chaining
- **Platform-agnostic execution** across any AI model

## 📊 **Stage-Based Organization**

### **Stage Ranges & Purposes**
```
Stage 1: 1000-1999 | Prototyping/Testing    | Auto-ID: ✓
Stage 2: 2000-2999 | Validated/Unplaced     | Auto-ID: ✗
Stage 3: 3000-3999 | Finalized/Production   | Auto-ID: ✗
Stage 4: 4000-4999 | Reserved               | Auto-ID: ✗
Stage 5: 5000-5999 | Reserved               | Auto-ID: ✗
Stage 6: 6000-6999 | Reserved               | Auto-ID: ✗
Stage 7: 7000-7999 | Reserved               | Auto-ID: ✗
Stage 8: 8000-8999 | Reserved               | Auto-ID: ✗
Stage 9: 9000-9999 | Reserved               | Auto-ID: ✗
```

### **Stage-First Organization**
```
# CRYSTALLIZED STRUCTURE (Prototyping Phase)
src/templates/
├── stage1/                    # 1000-1999: Prototyping/Testing
│   ├── generators/
│   │   └── 1000-1199.identifiers.py
│   ├── md/
│   │   ├── 1031-a-form_classifier.md
│   │   ├── 1031-b-form_classifier.md
│   │   ├── 1031-c-form_classifier.md
│   │   └── 1031-d-form_classifier.md
│   └── README.md
├── stage2/                    # 2000-2999: Validated/Unplaced
├── stage3/                    # 3000-3999: Finalized/Production
└── archive/                   # 34 archived templates preserved
```

## 🔧 **Three-Part Canonical Structure**

Every template follows this invariant pattern:
```
[Title] Interpretation Execute as: `{Transformation}`
  │      │              │         └─ Machine-parsable parameters
  │      │              └─ Standard connector phrase
  │      └─ Goal negation + transformation declaration
  └─ Brief identifier in brackets
```

### **Goal Negation Pattern**
The revolutionary approach of explicitly stating what NOT to do first:
```
"Your goal is not to **[action]** the input, but to **[transformation_action]** it..."
```

### **Syntactic Structure Focus**
Rather than domain-specific knowledge, the system focuses on **syntactic patterns**:
- `[keyword]` - Bracketed directives
- `[keyword:value]` - Parameterized directives
- `**text**` - Emphasis markers
- Structured transformation rules

## 🚀 **Quick Start (Crystallized System)**

### **Stage 1 Workflow**
```bash
# Generate crystallized templates
python src/templates/stage1/generators/1000-1199.identifiers.py

# Update catalog
python src/templates/lvl1_md_to_json.py --force

# View stage distribution
python src/templates/lvl1_md_to_json.py --stage-overview
```

### **Progressive Compression Demonstration**
```bash
# Execute the 1031 sequence (Form Classifier a-d)
python src/lvl1_sequence_executor.py --sequence 1031 --prompt "Your input text"

# Shows progressive compression:
# 1031-a: Comprehensive analysis
# 1031-b: Focused distillation
# 1031-c: Essential compression
# 1031-d: Absolute essence
```

### **Development Workflow**
```bash
# Start in stage1 (no distractions from other stages)
cd src/templates/stage1/

# Create new templates in generators/
# Test with rapid iteration
# Promote to stage2 when validated
```

## 📋 **Crystallized Sequence: 1031 (Form Classifier)**

### **Progressive Compression Demonstration**

#### **1031-a: Comprehensive Analysis**
```
[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`
```

#### **1031-b: Focused Distillation**
```
[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`
```

#### **1031-c: Essential Compression**
```
[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`
```

#### **1031-d: Absolute Essence**
```
[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`
```

## 🔄 **Workflow**

### **1. Prototyping (Stage 1)**
- Get next auto-ID for new template
- Create template with auto-generated ID
- Test and iterate in Stage 1 range
- No manual ID management required

### **2. Validation (Stage 2)**
- Move proven templates from Stage 1
- Templates are validated but not yet categorized
- Manual ID assignment for organization
- Prepare for final production placement

### **3. Production (Stage 3)**
- Finalized, production-ready templates
- Stable IDs that won't change
- Full compliance with RulesForAI.md
- Ready for system integration

## 🏗️ **System Architecture**

### **Template Categories**
- **Extractors (1000-1100)**: Extract specific elements from input
- **Generators (2000-2100)**: Generate content from scratch
- **Transformers (3000-3100)**: Transform input content using patterns
- **Classifiers (0500-0600)**: Categorize and classify content
- **Runway Templates (8000-8100)**: Specialized visual generation

### **Key Features**
- ✅ **Universal Compatibility**: Works across any AI model provider
- ✅ **Composability**: Templates can be chained together in sequences
- ✅ **Type Safety**: Built-in type checking for inputs/outputs
- ✅ **Systematic Validation**: Automatic compliance checking
- ✅ **Scalable Architecture**: Stage-based organization with expansion capacity

---

**Ready for systematic template management and AI instruction processing at scale.**
