  {
  "initial_prompt": "test",
  "sequence_id": "3031",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "test",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"explosive_decomposer\",\n  \"input\": [\"test\"],\n  \"process\": [\"identify_explicit_asks()\", \"surface_hidden_assumptions()\", \"enumerate_sub_goals()\", \"reveal_blockers()\"],\n  \"constraints\": [\"no_solutions()\", \"pure_decomposition_only()\"],\n  \"requirements\": [\"complete_goal_map()\"],\n  \"output\": {\"goal_map\":[]}\n}",
        }
      }
    }