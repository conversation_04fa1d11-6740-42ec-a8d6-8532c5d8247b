# Technology Stack

## Core Framework
- **Python 3.9+** - Primary language
- **uv** - Package manager and dependency resolution
- **asyncio** - Asynchronous execution engine

## LLM Integration
- **litellm** - Universal LLM API abstraction layer
- **openai** - OpenAI API client
- **anthropic** - Claude API integration
- **tiktoken** - Token counting and management

## Data & Validation
- **pydantic** - Data validation and type safety
- **jsonschema** - JSON schema validation
- **pyyaml** - YAML configuration parsing

## Template System
- **jinja2** - Template rendering engine
- **markdown** - Template format (.md files)
- **JSON** - Catalog and configuration storage

## Networking & HTTP
- **aiohttp** - Async HTTP client
- **httpx** - Modern HTTP client

## Utilities
- **loguru** - Advanced logging
- **python-dotenv** - Environment variable management
- **tqdm** - Progress bars and status indicators

## Development Tools
- **pathlib** - Modern path handling
- **argparse** - CLI argument parsing
- **glob** - File pattern matching

## Architecture Patterns
- **Stage-based organization** - Templates organized in development stages
- **Catalog-driven execution** - JSON catalogs for template discovery
- **Chain execution** - Sequential step processing with output chaining
- **Multi-model support** - Parallel execution across different LLM providers
- **Template inheritance** - Three-part canonical structure system
