#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3024: Generalized High-Value Improver
    "3024-a-signal_isolator": {
        "title": "Signal Isolator",
        "interpretation": "Your goal is not to **preserve** all content, but to **isolate** the highest-impact elements that drive actual value. Execute as:",
        "transformation": "`{role=signal_isolator; input=[any_input:str]; process=[identify_value_drivers(), eliminate_noise_elements(), extract_leverage_mechanisms()]; constraints=[ignore_verbose_explanations(), focus_impact_only()]; requirements=[maximum_signal_clarity(), zero_redundancy()]; output={isolated_signals:array}}`",
    },
    "3024-b-clarity_amplifier": {
        "title": "Clarity Amplifier",
        "interpretation": "Your goal is not to **explain** the signals, but to **amplify** their clarity and precision. Execute as:",
        "transformation": "`{role=clarity_amplifier; input=[isolated_signals:array]; process=[eliminate_ambiguity(), intensify_directness(), optimize_comprehension()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_clarity(), immediate_understanding()]; output={amplified_signals:array}}`",
    },
    "3024-c-universal_abstractor": {
        "title": "Universal Abstractor",
        "interpretation": "Your goal is not to **generalize** broadly, but to **abstract** to maximum transferable potency. Execute as:",
        "transformation": "`{role=universal_abstractor; input=[amplified_signals:array]; process=[extract_universal_patterns(), eliminate_context_constraints(), maximize_transferability()]; constraints=[preserve_actionable_core(), maintain_precision()]; requirements=[universal_applicability(), retained_potency()]; output={universal_directives:array}}`",
    },
    "3024-d-impact_crystallizer": {
        "title": "Impact Crystallizer",
        "interpretation": "Your goal is not to **format** the directives, but to **crystallize** them into maximum-impact form. Execute as:",
        "transformation": "`{role=impact_crystallizer; input=[universal_directives:array]; process=[compress_to_essence(), maximize_actionability(), ensure_immediate_implementability()]; constraints=[no_explanatory_content(), directive_format_only()]; requirements=[maximum_impact_density(), instant_actionability()]; output={crystallized_improver:str}}`",
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3024, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
