
    # SEQ:3200
    default_prompt =  """[SEQ:3200] Please write a new generalized instruction sequence designed to transform any input into uniquely optimized prompts specifically tailored for runway (based on knowledge gathered by community workflows). the attached example is tailored for video-prompts, but your goal is to write the prompt sequence specifically designed for image-generation prompts primarily:\n\n```\n\n    "0005-a-runway_prompt_generator": {\n        "title": "Runway Prompt Generator",\n        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:",\n        "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",\n    },\n    "0005-b-runway_prompt_generator": {\n        "title": "Runway Prompt Generator",\n        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:",\n        "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",\n    },\n    "0005-c-runway_prompt_generator": {\n        "title": "Runway Prompt Generator",\n        "interpretation": "Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:",\n        "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",\n    },\n    "0005-d-runway_prompt_generator": {\n        "title": "Runway Prompt Generator",\n        "interpretation": "Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as:",\n        "transformation": "`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",\n    },\n```"""
    default_prompt =  """[SEQ:3200] Intersecting geometric planes and shapes create a dark, dimensional space. The forms appear to be architectural or sculptural in nature, with sharp angles and overlapping elements emerging from deep shadows."""
    default_prompt =  """[SEQ:3200|3000|3201|3200] hyper realistic Norwegian landscaped garden with interlocking paving stones, lush lawn, stone retaining walls, corten steel beds, trimmed hedges, visible earthwork, natural stone paths, birch trees, seamless design [IMG] hyper-realistic contemporary residential garden with vibrant dense manicured green lawn in foreground, sharply defined weathered rust-orange Corten steel edging separating lawn from beds, linear compact hedge creating crisp upper boundary, evenly spaced climate-resilient shrubs and understated flowers with glossy and semi-matte leaves accent base of hedge, broad inviting composition with strong geometric structure, micro-detailed botanical and steel textures, soft natural ambient daylight cascading from above, clean professional landscaping, ground-level elevated viewpoint, photorealistic surfaces and crisp transitions"""
