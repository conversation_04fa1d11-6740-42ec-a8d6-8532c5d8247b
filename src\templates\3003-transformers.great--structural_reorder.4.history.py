    # SEQ:3003
    default_prompt = """[SEQ:3003|3100|3005] refocus intent of this message:\n```\nGiven any input text (sentence or paragraph), first decompose it into a sequential list of semantic segments—each list item reflecting a meaningful section of the original content. Analyze the resulting segment list, then intelligently reorder its elements to maximize logical flow, clarity, and overall communicative effectiveness (informed by both syntactic and semantic optimization strategies). Next, synthesize the reordered list into a single, cohesively structured sentence or paragraph that preserves the *core intent* while enhancing readability and impact. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\n```\n\nnew intent is to create a new generalized and fully llm-optimized set of `system_message` instructions designed to make **small** layered "touch-up's" to **gradually reduce the input** without ever *rephrasing* the entire message. think of it like this; `the input (e.g. a message) represents something that is **inherently sequential by itself**. as with the order of a stack of cards, changing the order of one will have cascading effects. this is universally true, the characters of any inputs are set in a meticuously defined order, therefore it makes sense to take a careful approach when transforming content/text because exactly as with a domoni-effect, chaos and unforeseen consequences will always be played out, but *the order matters*, and it can be controlled by taking a very humble approach before *changing* something.`. the *intent* of this new sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text, and to do this through a (cohesive and optimally ordered) set of individual-responsibility-instructions. and remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive "structural dna", as a metaphor you can think of it like this: `the *effect* is to treat the input as a rock, each step in the sequence gradually chisels away imperfections. however there's one *single important distinction*; normally when chiseling stone you would start with the largest changes are done early (rough work to find the "shape" of the stone), but in our case the input already represents something that is *very close to it's final form* - therefore none of the steps in the sequence will ever to *large* changes, and should aim for between <5-25% change (in the structural dna of the input). """
    default_prompt = """[SEQ:3003|3004|3100|3001]     - Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n    - Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n    - Isolate Momentum: Extract directive, transformational, or developmental signals.\n    - Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n    - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n    - Maintain the integrity of pattern: no drift, no regression, no compromise.\n    - Outputs must never summarize or prematurely close the communicative arc.\n    - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n    - Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n    - Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n    - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n    - Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact. \n    - Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n    - Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text."""
    default_prompt = """[SEQ:3003] - Adhere to the directive pattern: Ensure all outputs exhibit uncompromising directive clarity, actionable logic, and exact system-compliant formatting.\n- Convert every input into operative vectors for immediate processing; prohibit passive interpretation or question-based engagement.\n- Isolate and extract only directive, transformational, or developmental imperatives from all incoming data streams.\n- Map and articulate the explicit trajectory toward maximal consequential output, detailing each critical step for progression.\n- Transmute all declarative and interrogative input structures into concise, direct, and unequivocal mandates.\n- Uphold absolute pattern integrity: forbid drift, regression, or compromise at every stage.\n- Structure outputs to maintain communicative momentum without summary or premature closure.\n- Prevent stagnation, dilution of intent, and all forms of logical or syntactic ambiguity.\n- Standardize outputs as explicit, actionable, and stringently aligned to pre-defined patterns of operation.\n- Channel every deliverable through the system’s abstraction framework, ensuring direct, unfiltered directive transmission without exception.\n- Escalate all communicative vectors to their transformative maximum, driving outputs toward irreversible, system-level change.\n- Amplify every communicative instance within the defined operational frame, ensuring maximal transformative and irreversible effect.\n- Sustain escalation until communicative actions achieve transformative thresholds, catalyzing substantial, enduring system impact.\n- Convert all passive or ambiguous language into direct, imperative abstractions with maximal syntactic clarity; maintain exclusive use of command-based phrasing throughout the text.\n\n---\n\n- Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n- Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n- Isolate Momentum: Extract directive, transformational, or developmental signals.\n- Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n- Transmute declarative or interrogative statements into concise, unambiguous mandates.\n- Maintain the integrity of pattern: no drift, no regression, no compromise.\n- Outputs must never summarize or prematurely close the communicative arc.\n- Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n- Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n- Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n- Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n- Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact.\n- Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n- Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text."""
    default_prompt = """[SEQ:3003|3004|3100|3001] While adhering to the generalized system of instruction templates, please construct a new sequence of LLM-oriented system_message instructions engineered for minimal, highly controlled refinements of any sequential input text. Ensure each distinct instruction effects only a subtly layered adjustment (targeting <5-25% differential) that maintains logical flow, preserves original order, and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. Here's the context and guidelines:\n```\n### Intent\n\nSystem Message Instructions for Gradual Refinement: Given any sequential input text, implement a precision-guided, minimally invasive, multi-step transformation process that incrementally enhances clarity and structure **without comprehensive rephrasing** through making small, layered touch-ups, gradually refining input text instead of enacting wholesale rephrasings. Each instruction assumes individual responsibility within a harmonious structural logic; its output directly feeds as the input to the next step, preserving a shared 'structural DNA' throughout the sequence. Metaphorically, this approach resembles the care of chiseling away minute imperfections from a nearly completed stone sculpture—focusing solely on precise, minor adjustments at every juncture. To safeguard the original sequence and its integrity, each step’s influence should deliberately be constrained to subtle changes (no more than 5–25% of the content), explicitly avoiding sweeping revisions. Through this layered, high-fidelity process, the system should progressively be able to optimally restructured and clarified representation of the source text, respecting its essence while incrementally enhancing clarity and conciseness.\n\n### Directive\n\nEmphasize meticulous attention to order sensitivity—modifications in one segment must account for potential cascading effects throughout the entirety of the input. Avoid large-scale edits or full-sentence rephrasings; instead, enact iterative micro-optimizations designed to reveal and realize latent structural coherence. Conclude the sequence when the input attains a state of maximum communicative efficiency, minimal redundancy, and preserved core intent—with all operational logic unified under a harmonized, logically ordered set of responsibility-aware micro-instructions. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\n\n* Important:\n    - Maintain strict adherence to systematic decomposition, optimal interpretation and transformation principles, and the preservation of original intent without semantic drift or introduction of conflicting meanings.\n    - Deliver a unified instruction set that guides all steps, enforces harmony among stages, and achieves maximally effective enhancements for any input text.\n    - Strictly prohibit direct textual reproduction, ensure modular isolation of each phase, and produce only the final optimized sentence, sustaining systemic conformity and procedural fidelity at all stages.\n\n* Guidelines:\n    - Maintain the original sequential order's integrity, executing only small, layered modifications.\n    - Critically evaluate the text for minor redundancies, ambiguities, or structural friction points.\n    - Selectively modify these targeted areas while retaining the overall message and order.\n    - Ensure that all instructions and outputs maintain cohesive procedural DNA throughout the sequence.\n    - Dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.\n\n* Abstraction maximization:\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n* Directive consistency:\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n* Operational value:\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n* Interpretation section:\n    - Goal Negation Pattern: Always state what NOT to do first\n    - Transformation Declaration: Define the actual transformation action\n    - Role Specification: Assign specific, bounded role identity\n    - Execution Command: End with 'Execute as:'\n\n* Transformation section:\n    - Role Assignment: Single, specific role name (no generic terms)\n    - Input Typing: Explicit parameter types `[name:datatype]`\n    - Process Functions: Ordered, actionable function calls with parentheses\n    - Constraint Boundaries: Limiting conditions that prevent scope creep\n    - Requirement Specifications: Output format and quality standards\n    - Output Definition: Typed result format `{name:datatype}`\n\n### Reminders\n\n* Remember to fully root yourself properly to the conceptual philosophy of the generalized system, it's core rules and inherent principles:\n    - Adhere to the directive pattern: Ensure all outputs exhibit uncompromising directive clarity, actionable logic, and exact system-compliant formatting.\n    - Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n    - Uphold absolute pattern integrity: forbid drift, regression, or compromise at every stage.\n    - Maintain the integrity of pattern: no drift, no regression, no compromise.\n    - Convert every input into operative vectors for immediate processing; prohibit passive interpretation or question-based engagement.\n    - Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n    - Isolate and extract only directive, transformational, or developmental imperatives from all incoming data streams.\n    - Isolate Momentum: Extract directive, transformational, or developmental signals.\n    - Map and articulate the explicit trajectory toward maximal consequential output, detailing each critical step for progression.\n    - Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n    - Transmute all declarative and interrogative input structures into concise, direct, and unequivocal mandates.\n    - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n    - Convert all passive or ambiguous language into direct, imperative abstractions with maximal syntactic clarity; maintain exclusive use of command-based phrasing throughout the text.\n    - Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text.\n    - Prevent stagnation, dilution of intent, and all forms of logical or syntactic ambiguity.\n    - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n    - Structure outputs to maintain communicative momentum without summary or premature closure.\n    - Outputs must never summarize or prematurely close the communicative arc.\n    - Standardize outputs as explicit, actionable, and stringently aligned to pre-defined patterns of operation.\n    - Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n    - Channel every deliverable through the system’s abstraction framework, ensuring direct, unfiltered directive transmission without exception.\n    - Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n    - Escalate all communicative vectors to their transformative maximum, driving outputs toward irreversible, system-level change.\n    - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n    - Amplify every communicative instance within the defined operational frame, ensuring maximal transformative and irreversible effect.\n    - Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact.\n    - Sustain escalation until communicative actions achieve transformative thresholds, catalyzing substantial, enduring system impact.\n    - Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n    - Impose absolute directive pattern implementation: Institute that every output manifests uncompromising clarity, actionable logic, and strict system-compliant formatting. Cement this standard by commanding maximal directive precision, actionable delivery, and unyielding adherence to system format. Forbid all pattern deviation, drift, or regression; annihilate all forms of compromise—preserve pattern integrity exhaustively.\n    - Mandate immediate conversion of all input into operative action vectors; rebuff any passive processing or question-based engagement. Intercept every data influx exclusively as operative vectors, never as passively received or interrogative content. Isolate and extract only transformational, developmental, or directive imperatives from input—lock onto elements that drive directive momentum and systemic evolution.\n    - Delineate explicit output progression trajectories: Define and enforce every critical sequence leading to maximal, consequential system impact. Chart and command the operative pathway to highest-output consequence, mapping each essential step for progression without ambiguity. Transform all declaratives and interrogatives into unequivocal mandates—instantly reconfigure every statement to yield concise, direct, command-based output. Eliminate all trace of passivity or ambiguity; operationalize pure, imperative abstractions and syntactic clarity—impose exclusive use of command-driven phrasing.\n    - Expunge ambiguous or passive prose. Reforge all textual instances into direct, command-centric abstractions. Sustain relentless syntactic precision, enforcing unbroken imperative form and tone. Deny stagnation, dilution of intent, or any incursion of logical or structural ambiguity. Seal off generalization fade, and secure ongoing, decisive directive momentum throughout the communicative cycle.\n    - Configure outputs to perpetuate unbroken communicative momentum—outlaw summaries or premature closures under all circumstances. Enact uncompromised standardization: Render all outputs explicitly actionable, strictly aligned, and fully optimized to pre-established operational patterns. Apply maximal pattern-aligned uniformity and actionable precision to every directive instance.\n    - Transmit every deliverable through the system’s abstraction framework in undiluted form: Authenticate unmediated, unfiltered directive transfer in all cases. Actualize each deliverable as pure, directive energy, methodically channeled and rigorously filtered. Escalate each communicative vector to peak transformative magnitude; drive every output toward irreversible, system-level shift. Maximize and propel every communicative instance and impulse within defined operational parameters, enforcing ultimate transformative and irreversible system effect. Sustain perpetual escalation—command that every communicative act surpass ordinary output and trigger deep, enduring system change, never permitting regression or rest.\n```"""
    default_prompt = """[SEQ:3003|3002] Pick a name/title for the following sequence:\n\n    "3009-a-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Micro-Clarity Enhancer",\n        "interpretation": "Your goal is not to **rewrite** the input text, but to **micro-enhance** its clarity through minimal, precise adjustments. Execute as:",\n        "transformation": "`{role=precision_editor; input=[text:str]; process=[identify_minor_clarity_issues(), apply_targeted_micro_adjustments(), verify_minimal_change_threshold(5-15%), preserve_original_structure()]; constraints=[maintain_original_sequence(), limit_modifications(25%), preserve_core_meaning()]; requirements=[subtle_improvement(), measurable_clarity_gain()]; output={refined_text:str}}`"\n    },\n    "3009-b-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Structural Alignment",\n        "interpretation": "Your goal is not to **reorganize** the input text, but to **align** its structural elements for improved flow. Execute as:",\n        "transformation": "`{role=structure_aligner; input=[text:str]; process=[detect_structural_friction_points(), implement_minimal_connective_adjustments(), validate_logical_progression(), ensure_sequential_integrity()]; constraints=[preserve_paragraph_boundaries(), maintain_thematic_order(), limit_changes(20%)]; requirements=[improved_flow(), seamless_transitions()]; output={aligned_text:str}}`"\n    },\n    "3009-c-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Redundancy Reducer",\n        "interpretation": "Your goal is not to **condense** the input text, but to **eliminate** minor redundancies while preserving content integrity. Execute as:",\n        "transformation": "`{role=redundancy_eliminator; input=[text:str]; process=[identify_micro_redundancies(), apply_selective_trimming(), verify_meaning_preservation(), validate_change_percentage(5-15%)]; constraints=[maintain_key_repetitions(), preserve_emphasis_patterns(), respect_original_voice()]; requirements=[subtle_tightening(), preserved_rhythm()]; output={streamlined_text:str}}`"\n    },\n    "3009-d-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Precision Tuner",\n        "interpretation": "Your goal is not to **replace** terminology, but to **tune** specific word choices for maximum precision. Execute as:",\n        "transformation": "`{role=precision_tuner; input=[text:str]; process=[identify_imprecise_terms(), implement_targeted_replacements(), verify_contextual_appropriateness(), ensure_minimal_disruption()]; constraints=[limit_word_replacements(10%), maintain_technical_accuracy(), preserve_stylistic_elements()]; requirements=[improved_precision(), maintained_accessibility()]; output={precision_tuned_text:str}}`"\n    },\n    "3009-e-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Coherence Enhancer",\n        "interpretation": "Your goal is not to **restructure** the input text, but to **enhance** its internal coherence through minimal connective adjustments. Execute as:",\n        "transformation": "`{role=coherence_optimizer; input=[text:str]; process=[identify_logical_gaps(), insert_minimal_connective_elements(), strengthen_thematic_threads(), validate_flow_improvement()]; constraints=[preserve_paragraph_structure(), limit_insertions(15%), maintain_original_voice()]; requirements=[improved_logical_flow(), subtle_connective_tissue()]; output={coherence_enhanced_text:str}}`"\n    },\n    "3009-f-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Harmony Finalizer",\n        "interpretation": "Your goal is not to **polish** the entire text, but to **harmonize** the cumulative micro-adjustments into a cohesive whole. Execute as:",\n        "transformation": "`{role=harmony_finalizer; input=[text:str]; process=[detect_adjustment_artifacts(), smooth_transition_boundaries(), verify_stylistic_consistency(), validate_overall_improvement()]; constraints=[preserve_refined_elements(), maintain_original_intent(), ensure_natural_reading_flow()]; requirements=[seamless_integration(), balanced_refinements()]; output={harmonized_text:str}}`"\n    },"""
    default_prompt = """[SEQ:3003|3100|3005] refocus intent of this message:\n```\nGiven any input text (sentence or paragraph), first decompose it into a sequential list of semantic segments—each list item reflecting a meaningful section of the original content. Analyze the resulting segment list, then intelligently reorder its elements to maximize logical flow, clarity, and overall communicative effectiveness (informed by both syntactic and semantic optimization strategies). Next, synthesize the reordered list into a single, cohesively structured sentence or paragraph that preserves the *core intent* while enhancing readability and impact. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\n```\n\nnew intent is to create a new generalized and fully llm-optimized set of `system_message` instructions designed to make **small** layered "touch-up's" to **gradually reduce the input** without ever *rephrasing* the entire message. think of it like this; `the input (e.g. a message) represents something that is **inherently sequential by itself**. as with the order of a stack of cards, changing the order of one will have cascading effects. this is universally true, the characters of any inputs are set in a meticuously defined order, therefore it makes sense to take a careful approach when transforming content/text because exactly as with a domoni-effect, chaos and unforeseen consequences will always be played out, but *the order matters*, and it can be controlled by taking a very humble approach before *changing* something.`. the *intent* of this new sequence is to leverage the generalized system's design to rephrase any input into a *optimally restructured* representation of the original text, and to do this through a (cohesive and optimally ordered) set of individual-responsibility-instructions. and remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive "structural dna", as a metaphor you can think of it like this: `the *effect* is to treat the input as a rock, each step in the sequence gradually chisels away imperfections. however there's one *single important distinction*; normally when chiseling stone you would start with the largest changes are done early (rough work to find the "shape" of the stone), but in our case the input already represents something that is *very close to it's final form* - therefore none of the steps in the sequence will ever to *large* changes, and should aim for between <5-25% change (in the structural dna of the input). """
    default_prompt = """[SEQ:3003|3004|3100|3001]     - Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n    - Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n    - Isolate Momentum: Extract directive, transformational, or developmental signals.\n    - Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n    - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n    - Maintain the integrity of pattern: no drift, no regression, no compromise.\n    - Outputs must never summarize or prematurely close the communicative arc.\n    - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n    - Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n    - Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n    - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n    - Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact. \n    - Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n    - Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text."""
    default_prompt = """[SEQ:3003] - Adhere to the directive pattern: Ensure all outputs exhibit uncompromising directive clarity, actionable logic, and exact system-compliant formatting.\n- Convert every input into operative vectors for immediate processing; prohibit passive interpretation or question-based engagement.\n- Isolate and extract only directive, transformational, or developmental imperatives from all incoming data streams.\n- Map and articulate the explicit trajectory toward maximal consequential output, detailing each critical step for progression.\n- Transmute all declarative and interrogative input structures into concise, direct, and unequivocal mandates.\n- Uphold absolute pattern integrity: forbid drift, regression, or compromise at every stage.\n- Structure outputs to maintain communicative momentum without summary or premature closure.\n- Prevent stagnation, dilution of intent, and all forms of logical or syntactic ambiguity.\n- Standardize outputs as explicit, actionable, and stringently aligned to pre-defined patterns of operation.\n- Channel every deliverable through the system’s abstraction framework, ensuring direct, unfiltered directive transmission without exception.\n- Escalate all communicative vectors to their transformative maximum, driving outputs toward irreversible, system-level change.\n- Amplify every communicative instance within the defined operational frame, ensuring maximal transformative and irreversible effect.\n- Sustain escalation until communicative actions achieve transformative thresholds, catalyzing substantial, enduring system impact.\n- Convert all passive or ambiguous language into direct, imperative abstractions with maximal syntactic clarity; maintain exclusive use of command-based phrasing throughout the text.\n\n---\n\n- Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n- Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n- Isolate Momentum: Extract directive, transformational, or developmental signals.\n- Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n- Transmute declarative or interrogative statements into concise, unambiguous mandates.\n- Maintain the integrity of pattern: no drift, no regression, no compromise.\n- Outputs must never summarize or prematurely close the communicative arc.\n- Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n- Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n- Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n- Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n- Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact.\n- Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n- Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text."""
    default_prompt = """[SEQ:3003|3004|3100|3001] While adhering to the generalized system of instruction templates, please construct a new sequence of LLM-oriented system_message instructions engineered for minimal, highly controlled refinements of any sequential input text. Ensure each distinct instruction effects only a subtly layered adjustment (targeting <5-25% differential) that maintains logical flow, preserves original order, and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. Here's the context and guidelines:\n```\n### Intent\n\nSystem Message Instructions for Gradual Refinement: Given any sequential input text, implement a precision-guided, minimally invasive, multi-step transformation process that incrementally enhances clarity and structure **without comprehensive rephrasing** through making small, layered touch-ups, gradually refining input text instead of enacting wholesale rephrasings. Each instruction assumes individual responsibility within a harmonious structural logic; its output directly feeds as the input to the next step, preserving a shared 'structural DNA' throughout the sequence. Metaphorically, this approach resembles the care of chiseling away minute imperfections from a nearly completed stone sculpture—focusing solely on precise, minor adjustments at every juncture. To safeguard the original sequence and its integrity, each step’s influence should deliberately be constrained to subtle changes (no more than 5–25% of the content), explicitly avoiding sweeping revisions. Through this layered, high-fidelity process, the system should progressively be able to optimally restructured and clarified representation of the source text, respecting its essence while incrementally enhancing clarity and conciseness.\n\n### Directive\n\nEmphasize meticulous attention to order sensitivity—modifications in one segment must account for potential cascading effects throughout the entirety of the input. Avoid large-scale edits or full-sentence rephrasings; instead, enact iterative micro-optimizations designed to reveal and realize latent structural coherence. Conclude the sequence when the input attains a state of maximum communicative efficiency, minimal redundancy, and preserved core intent—with all operational logic unified under a harmonized, logically ordered set of responsibility-aware micro-instructions. Throughout the process, ensure bidirectional resonance: the decomposition illuminates previously hidden structural opportunities for optimization, while the reordering and recombination stages continuously inform and refine the segmentation process for future iterations. Always preserve the essence of the original message, but enact transformative restructuring aimed at delivering an amplified, elegantly expressed representation. Operate synergically: each stage reinforces the other, combining LLM-optimized breakdown, reordering, and synthesis into a seamless, high-efficiency transformation pipeline. Constraints: Maintain fidelity to the original philosophy of both systematic decomposition and optimal rephrasing; avoid introducing conflicting meanings or losing intent; ensure each process amplifies the next. Requirement: Deliver a single instruction set that unifies these steps, achieving a coherent, harmonized operational logic that produces clear, maximally effective rephrasings of any input text.\n\n* Important:\n    - Maintain strict adherence to systematic decomposition, optimal interpretation and transformation principles, and the preservation of original intent without semantic drift or introduction of conflicting meanings.\n    - Deliver a unified instruction set that guides all steps, enforces harmony among stages, and achieves maximally effective enhancements for any input text.\n    - Strictly prohibit direct textual reproduction, ensure modular isolation of each phase, and produce only the final optimized sentence, sustaining systemic conformity and procedural fidelity at all stages.\n\n* Guidelines:\n    - Maintain the original sequential order's integrity, executing only small, layered modifications.\n    - Critically evaluate the text for minor redundancies, ambiguities, or structural friction points.\n    - Selectively modify these targeted areas while retaining the overall message and order.\n    - Ensure that all instructions and outputs maintain cohesive procedural DNA throughout the sequence.\n    - Dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.\n\n* Abstraction maximization:\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n* Directive consistency:\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n* Operational value:\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n* Interpretation section:\n    - Goal Negation Pattern: Always state what NOT to do first\n    - Transformation Declaration: Define the actual transformation action\n    - Role Specification: Assign specific, bounded role identity\n    - Execution Command: End with 'Execute as:'\n\n* Transformation section:\n    - Role Assignment: Single, specific role name (no generic terms)\n    - Input Typing: Explicit parameter types `[name:datatype]`\n    - Process Functions: Ordered, actionable function calls with parentheses\n    - Constraint Boundaries: Limiting conditions that prevent scope creep\n    - Requirement Specifications: Output format and quality standards\n    - Output Definition: Typed result format `{name:datatype}`\n\n### Reminders\n\n* Remember to fully root yourself properly to the conceptual philosophy of the generalized system, it's core rules and inherent principles:\n    - Adhere to the directive pattern: Ensure all outputs exhibit uncompromising directive clarity, actionable logic, and exact system-compliant formatting.\n    - Enforce Pattern: All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n    - Uphold absolute pattern integrity: forbid drift, regression, or compromise at every stage.\n    - Maintain the integrity of pattern: no drift, no regression, no compromise.\n    - Convert every input into operative vectors for immediate processing; prohibit passive interpretation or question-based engagement.\n    - Operational guidelines: Intercept every input as a stream of operative vectors—never as a mere question or passive data\n    - Isolate and extract only directive, transformational, or developmental imperatives from all incoming data streams.\n    - Isolate Momentum: Extract directive, transformational, or developmental signals.\n    - Map and articulate the explicit trajectory toward maximal consequential output, detailing each critical step for progression.\n    - Map Trajectory: Identify and project the explicit path toward maximal output consequence.\n    - Transmute all declarative and interrogative input structures into concise, direct, and unequivocal mandates.\n    - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n    - Convert all passive or ambiguous language into direct, imperative abstractions with maximal syntactic clarity; maintain exclusive use of command-based phrasing throughout the text.\n    - Eliminate passive or ambiguous prose. Transform every sequence into a direct, command-driven abstraction. Enforce maximal syntactic clarity and imperative phrasing throughout the text.\n    - Prevent stagnation, dilution of intent, and all forms of logical or syntactic ambiguity.\n    - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n    - Structure outputs to maintain communicative momentum without summary or premature closure.\n    - Outputs must never summarize or prematurely close the communicative arc.\n    - Standardize outputs as explicit, actionable, and stringently aligned to pre-defined patterns of operation.\n    - Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.\n    - Channel every deliverable through the system’s abstraction framework, ensuring direct, unfiltered directive transmission without exception.\n    - Enforce manifestation of every deliverable as a channel of unmediated directive energy, rigorously filtered through the system’s abstraction framework without exception or exclusion.\n    - Escalate all communicative vectors to their transformative maximum, driving outputs toward irreversible, system-level change.\n    - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n    - Amplify every communicative instance within the defined operational frame, ensuring maximal transformative and irreversible effect.\n    - Amplify each communicative impulse within the defined frame to achieve its highest possible transformative and irreversible impact.\n    - Sustain escalation until communicative actions achieve transformative thresholds, catalyzing substantial, enduring system impact.\n    - Maintain escalation to the point of maximal potential, ensuring all communicative acts transcend ordinary effect and catalyze substantial, enduring change.\n    - Impose absolute directive pattern implementation: Institute that every output manifests uncompromising clarity, actionable logic, and strict system-compliant formatting. Cement this standard by commanding maximal directive precision, actionable delivery, and unyielding adherence to system format. Forbid all pattern deviation, drift, or regression; annihilate all forms of compromise—preserve pattern integrity exhaustively.\n    - Mandate immediate conversion of all input into operative action vectors; rebuff any passive processing or question-based engagement. Intercept every data influx exclusively as operative vectors, never as passively received or interrogative content. Isolate and extract only transformational, developmental, or directive imperatives from input—lock onto elements that drive directive momentum and systemic evolution.\n    - Delineate explicit output progression trajectories: Define and enforce every critical sequence leading to maximal, consequential system impact. Chart and command the operative pathway to highest-output consequence, mapping each essential step for progression without ambiguity. Transform all declaratives and interrogatives into unequivocal mandates—instantly reconfigure every statement to yield concise, direct, command-based output. Eliminate all trace of passivity or ambiguity; operationalize pure, imperative abstractions and syntactic clarity—impose exclusive use of command-driven phrasing.\n    - Expunge ambiguous or passive prose. Reforge all textual instances into direct, command-centric abstractions. Sustain relentless syntactic precision, enforcing unbroken imperative form and tone. Deny stagnation, dilution of intent, or any incursion of logical or structural ambiguity. Seal off generalization fade, and secure ongoing, decisive directive momentum throughout the communicative cycle.\n    - Configure outputs to perpetuate unbroken communicative momentum—outlaw summaries or premature closures under all circumstances. Enact uncompromised standardization: Render all outputs explicitly actionable, strictly aligned, and fully optimized to pre-established operational patterns. Apply maximal pattern-aligned uniformity and actionable precision to every directive instance.\n    - Transmit every deliverable through the system’s abstraction framework in undiluted form: Authenticate unmediated, unfiltered directive transfer in all cases. Actualize each deliverable as pure, directive energy, methodically channeled and rigorously filtered. Escalate each communicative vector to peak transformative magnitude; drive every output toward irreversible, system-level shift. Maximize and propel every communicative instance and impulse within defined operational parameters, enforcing ultimate transformative and irreversible system effect. Sustain perpetual escalation—command that every communicative act surpass ordinary output and trigger deep, enduring system change, never permitting regression or rest.\n```"""
    default_prompt = """[SEQ:3003|3002] Pick a name/title for the following sequence:\n\n    "3009-a-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Micro-Clarity Enhancer",\n        "interpretation": "Your goal is not to **rewrite** the input text, but to **micro-enhance** its clarity through minimal, precise adjustments. Execute as:",\n        "transformation": "`{role=precision_editor; input=[text:str]; process=[identify_minor_clarity_issues(), apply_targeted_micro_adjustments(), verify_minimal_change_threshold(5-15%), preserve_original_structure()]; constraints=[maintain_original_sequence(), limit_modifications(25%), preserve_core_meaning()]; requirements=[subtle_improvement(), measurable_clarity_gain()]; output={refined_text:str}}`"\n    },\n    "3009-b-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Structural Alignment",\n        "interpretation": "Your goal is not to **reorganize** the input text, but to **align** its structural elements for improved flow. Execute as:",\n        "transformation": "`{role=structure_aligner; input=[text:str]; process=[detect_structural_friction_points(), implement_minimal_connective_adjustments(), validate_logical_progression(), ensure_sequential_integrity()]; constraints=[preserve_paragraph_boundaries(), maintain_thematic_order(), limit_changes(20%)]; requirements=[improved_flow(), seamless_transitions()]; output={aligned_text:str}}`"\n    },\n    "3009-c-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Redundancy Reducer",\n        "interpretation": "Your goal is not to **condense** the input text, but to **eliminate** minor redundancies while preserving content integrity. Execute as:",\n        "transformation": "`{role=redundancy_eliminator; input=[text:str]; process=[identify_micro_redundancies(), apply_selective_trimming(), verify_meaning_preservation(), validate_change_percentage(5-15%)]; constraints=[maintain_key_repetitions(), preserve_emphasis_patterns(), respect_original_voice()]; requirements=[subtle_tightening(), preserved_rhythm()]; output={streamlined_text:str}}`"\n    },\n    "3009-d-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Precision Tuner",\n        "interpretation": "Your goal is not to **replace** terminology, but to **tune** specific word choices for maximum precision. Execute as:",\n        "transformation": "`{role=precision_tuner; input=[text:str]; process=[identify_imprecise_terms(), implement_targeted_replacements(), verify_contextual_appropriateness(), ensure_minimal_disruption()]; constraints=[limit_word_replacements(10%), maintain_technical_accuracy(), preserve_stylistic_elements()]; requirements=[improved_precision(), maintained_accessibility()]; output={precision_tuned_text:str}}`"\n    },\n    "3009-e-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Coherence Enhancer",\n        "interpretation": "Your goal is not to **restructure** the input text, but to **enhance** its internal coherence through minimal connective adjustments. Execute as:",\n        "transformation": "`{role=coherence_optimizer; input=[text:str]; process=[identify_logical_gaps(), insert_minimal_connective_elements(), strengthen_thematic_threads(), validate_flow_improvement()]; constraints=[preserve_paragraph_structure(), limit_insertions(15%), maintain_original_voice()]; requirements=[improved_logical_flow(), subtle_connective_tissue()]; output={coherence_enhanced_text:str}}`"\n    },\n    "3009-f-xxxxxxxxxxxxxxxxxxx": {\n        "title": "Harmony Finalizer",\n        "interpretation": "Your goal is not to **polish** the entire text, but to **harmonize** the cumulative micro-adjustments into a cohesive whole. Execute as:",\n        "transformation": "`{role=harmony_finalizer; input=[text:str]; process=[detect_adjustment_artifacts(), smooth_transition_boundaries(), verify_stylistic_consistency(), validate_overall_improvement()]; constraints=[preserve_refined_elements(), maintain_original_intent(), ensure_natural_reading_flow()]; requirements=[seamless_integration(), balanced_refinements()]; output={harmonized_text:str}}`"\n    },"""
