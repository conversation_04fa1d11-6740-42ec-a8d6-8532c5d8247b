#!/usr/bin/env python3
"""
Test script for the unified display system
Verifies that EXECUTION_DEFAULTS are respected and display is consistent
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from display import (
    set_display_options, print_step, print_system_instruction, 
    print_response, print_system_status, print_file_operation,
    validate_display_settings, DisplayTheme
)

def test_display_consistency():
    """Test the unified display system with different configurations."""
    
    print("🧪 TESTING UNIFIED DISPLAY SYSTEM")
    print("=" * 50)
    
    # Test 1: All displays enabled (default)
    print("\n📋 TEST 1: All displays enabled")
    set_display_options(show_inputs=True, show_system_instructions=True, show_responses=True)
    validate_display_settings()
    
    print_step(1, "test-template", "This is a test input prompt", 0)
    print_system_instruction("This is a test system instruction", 1, "test-template")
    print_response('{"test": "This is a test response"}', "test-model")
    print_system_status("Test system message", True)
    print_file_operation("Test file operation", "/test/path.txt", True)
    
    # Test 2: Only responses enabled
    print("\n📋 TEST 2: Only responses enabled")
    set_display_options(show_inputs=False, show_system_instructions=False, show_responses=True)
    validate_display_settings()
    
    print_step(2, "test-template", "This input should be hidden", 0)
    print_system_instruction("This system instruction should be hidden", 2, "test-template")
    print_response('{"test": "This response should be visible"}', "test-model")
    print_system_status("This system message should be visible", True)
    
    # Test 3: Only inputs enabled
    print("\n📋 TEST 3: Only inputs enabled")
    set_display_options(show_inputs=True, show_system_instructions=False, show_responses=False)
    validate_display_settings()
    
    print_step(3, "test-template", "This input should be visible", 0)
    print_system_instruction("This system instruction should be hidden", 3, "test-template")
    print_response('{"test": "This response should be hidden"}', "test-model")
    print_system_status("This system message should be hidden", True)
    print_file_operation("This file operation should be hidden", "/test/path.txt", True)
    
    # Test 4: Force show system messages
    print("\n📋 TEST 4: Force show system messages (even when responses disabled)")
    set_display_options(show_inputs=False, show_system_instructions=False, show_responses=False)
    validate_display_settings()
    
    print_system_status("This forced system message should be visible", True, force_show=True)
    print_file_operation("This forced file operation should be visible", "/test/path.txt", True, force_show=True)
    print_system_status("This normal system message should be hidden", True, force_show=False)
    
    # Test 5: Color theme verification
    print("\n📋 TEST 5: Color theme verification")
    set_display_options(show_inputs=True, show_system_instructions=True, show_responses=True)
    
    from rich.console import Console
    console = Console()
    
    console.print("🎨 Color Theme Test:", style="bold")
    console.print(f"INPUT color: {DisplayTheme.INPUT}", style=DisplayTheme.INPUT)
    console.print(f"SYSTEM color: {DisplayTheme.SYSTEM}", style=DisplayTheme.SYSTEM)
    console.print(f"RESPONSE color: {DisplayTheme.RESPONSE}", style=DisplayTheme.RESPONSE)
    console.print(f"SUCCESS color: {DisplayTheme.SUCCESS}", style=DisplayTheme.SUCCESS)
    console.print(f"WARNING color: {DisplayTheme.WARNING}", style=DisplayTheme.WARNING)
    console.print(f"ERROR color: {DisplayTheme.ERROR}", style=DisplayTheme.ERROR)
    console.print(f"INFO color: {DisplayTheme.INFO}", style=DisplayTheme.INFO)
    console.print(f"SEPARATOR color: {DisplayTheme.SEPARATOR}", style=DisplayTheme.SEPARATOR)
    
    print("\n✅ DISPLAY SYSTEM TEST COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    test_display_consistency()
