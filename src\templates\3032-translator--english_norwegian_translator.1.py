#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 3032: English to Norwegian Translator
    "3032-a-english_norwegian_translator": {
        "title": "English Norwegian Translator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic English-to-Norwegian translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the English source. Make certain the Norwegian translation flows naturally, preserves the source's intent, and enhances nuanced expression. Execute as:",
        "transformation": "`{role=cultural_norwegian_translator; input=[english_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_structure(), preserve_sentence_structure(), maintain_cultural_authenticity()]; requirements=[flowing_norwegian(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3032, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
