#!/usr/bin/env python3
"""
Unified Display System for AI Systems
Single-rooted architecture for all terminal output with consistent Rich formatting
"""

from rich.console import Console
from rich.syntax import Syntax
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
import json
import re
import sys

# Global console instance
console = Console()

# Import centralized configuration
try:
    from config import EXECUTION_DEFAULTS
except ImportError:
    # Fallback defaults if config import fails
    EXECUTION_DEFAULTS = {
        "show_inputs": True,
        "show_system_instructions": True,
        "show_responses": True
    }

# =============================================================================
# UNIFIED COLOR SCHEME
# =============================================================================

class DisplayTheme:
    """Centralized color scheme for consistent terminal output."""

    # Primary colors for different content types
    INPUT = "yellow"           # User inputs and prompts
    SYSTEM = "magenta"         # System instructions
    RESPONSE = "blue"          # AI responses
    SUCCESS = "green"          # Success messages
    WARNING = "orange3"        # Warnings
    ERROR = "red"              # Errors
    INFO = "cyan"              # Information
    SEPARATOR = "dim cyan"     # Step separators

    # Text styles
    HEADER = "bold"
    DIM = "dim"
    BRIGHT = "bright_white"

# =============================================================================
# UNIFIED DISPLAY CONTROLLER
# =============================================================================

class DisplayController:
    """Centralized display controller that respects EXECUTION_DEFAULTS."""

    def __init__(self, show_inputs=None, show_system_instructions=None, show_responses=None):
        """Initialize with optional overrides for display settings."""
        self.show_inputs = show_inputs if show_inputs is not None else EXECUTION_DEFAULTS["show_inputs"]
        self.show_system_instructions = show_system_instructions if show_system_instructions is not None else EXECUTION_DEFAULTS["show_system_instructions"]
        self.show_responses = show_responses if show_responses is not None else EXECUTION_DEFAULTS["show_responses"]

    def should_show_inputs(self):
        """Check if inputs should be displayed."""
        return self.show_inputs

    def should_show_system_instructions(self):
        """Check if system instructions should be displayed."""
        return self.show_system_instructions

    def should_show_responses(self):
        """Check if responses should be displayed."""
        return self.show_responses

# =============================================================================
# UNIFIED OUTPUT FUNCTIONS
# =============================================================================

class UnifiedDisplay:
    """Single-rooted display system for all terminal output."""

    @staticmethod
    def system_message(message: str, level: str = "info", force_show: bool = False):
        """Display system messages with consistent formatting."""
        # System messages can be forced to show even when responses are hidden
        if not force_show and not _display_controller.should_show_responses():
            return

        colors = {
            "info": DisplayTheme.INFO,
            "success": DisplayTheme.SUCCESS,
            "warning": DisplayTheme.WARNING,
            "error": DisplayTheme.ERROR
        }

        color = colors.get(level, DisplayTheme.INFO)
        console.print(f"[System] {message}", style=color)

    @staticmethod
    def file_operation(message: str, path: str = None, success: bool = True, force_show: bool = False):
        """Display file operation messages with consistent formatting."""
        # File operations can be forced to show for important system messages
        if not force_show and not _display_controller.should_show_responses():
            return

        level = "success" if success else "error"
        if path:
            full_message = f"{message}: '{path}'"
        else:
            full_message = message

        UnifiedDisplay.system_message(full_message, level, force_show)

# Global display controller instance
_display_controller = DisplayController()

def set_display_options(show_inputs=None, show_system_instructions=None, show_responses=None):
    """Set global display options."""
    global _display_controller
    _display_controller = DisplayController(show_inputs, show_system_instructions, show_responses)

def get_display_controller():
    """Get the current display controller."""
    return _display_controller

# =============================================================================
# LEGACY COMPATIBILITY FUNCTIONS (Updated to use unified system)
# =============================================================================


def print_json(data, title=None):
    """Display JSON with syntax highlighting - unified system."""
    if not _display_controller.should_show_responses():
        return

    json_str = json.dumps(data, indent=2, ensure_ascii=False)
    syntax = Syntax(json_str, "json", theme="monokai", line_numbers=False, word_wrap=True)
    if title:
        console.print(Panel(syntax, title=title, border_style=DisplayTheme.RESPONSE))
    else:
        console.print(syntax)


def print_streaming_text(text, end="", flush=True):
    """Print streaming text using rich console - respects display controller."""
    if not _display_controller.should_show_responses():
        return

    console.print(text, end=end)
    if flush:
        console.file.flush()


def print_text(text, style=None):
    """Print text using rich console with optional styling - respects display controller."""
    if not _display_controller.should_show_responses():
        return

    console.print(text, style=style)


def print_sequences(sequences):
    """Display available sequences in a clean table - unified system."""
    table = Table(
        title="Available Sequences",
        show_header=True,
        header_style=f"bold {DisplayTheme.SYSTEM}"
    )
    table.add_column("ID", style=DisplayTheme.INFO, width=8)
    table.add_column("Name", style=DisplayTheme.SUCCESS)
    table.add_column("Steps", style=DisplayTheme.INPUT, width=8)
    table.add_column("Example", style=DisplayTheme.DIM)

    for seq_id, seq_data in sequences.items():
        steps_count = len(seq_data.get('steps', {}))
        name = seq_data.get('name', 'Unknown')
        example = f'--sequence "{seq_id}"'
        table.add_row(seq_id, name, str(steps_count), example)

    console.print(table)


def print_params(params, title="Parameters"):
    """Display parameters in a clean panel - unified system."""
    content = "\n".join(f"{k}: {v}" for k, v in params.items())
    console.print(Panel(f"\n{content}\n", title=title, border_style=DisplayTheme.INFO))


def print_initial_prompt(prompt):
    """Display initial prompt with distinct color - unified system."""
    if not _display_controller.should_show_inputs():
        return

    console.print(Panel(f"\n{prompt}\n", title="INITIAL PROMPT", border_style=DisplayTheme.SUCCESS))


def print_step(step_num, template_name, prompt, step_index=0):
    """Display execution step with highlighting - unified system."""
    if not _display_controller.should_show_inputs():
        return

    title = f"INPUT: [{step_num:03d}] {template_name}"

    # For steps after the first, replace embedded initial prompt with placeholder
    display_prompt = prompt
    if step_index > 0:
        # Look for the initial prompt pattern and replace with condensed version
        # Pattern for JSON field with initial prompt
        json_pattern = (r'"initial_prompt":\s*"\\n\'```\[Initial Prompt\]:\s*'
                        r'\\"([^"]+)\\"```\'\\n\\n",')
        # Pattern for standalone text
        text_pattern = r"\\n'```\[Initial Prompt\]:\s*\"([^\"]+)\"```'\\n\\n"

        match = re.search(json_pattern, prompt)
        if match:
            replacement = '"initial_prompt": "<initial prompt>",'
            display_prompt = re.sub(json_pattern, replacement, prompt)
        else:
            match = re.search(text_pattern, prompt)
            if match:
                replacement = "<initial prompt>"
                display_prompt = re.sub(text_pattern, replacement, prompt)

    console.print(Panel(f"\n{display_prompt}\n", title=title, border_style=DisplayTheme.INPUT))


def print_system_instruction(instruction, step_num, template_name):
    """Display system instruction with step number and template name - unified system."""
    if not _display_controller.should_show_system_instructions():
        return

    title = f"SYSTEM INSTRUCTION: [{step_num:03d}] {template_name}"

    # Try to detect if the instruction contains structured content that should be syntax highlighted
    if instruction.strip().startswith(('`{', '{')) and instruction.strip().endswith(('}', '}`')):
        # This looks like JSON or structured content, apply syntax highlighting
        syntax = Syntax(instruction, "json", theme="monokai", line_numbers=False, word_wrap=True)
        console.print(Panel(syntax, title=title, border_style=DisplayTheme.SYSTEM))
    else:
        # Plain text instruction, display as-is but with proper formatting
        console.print(Panel(f"\n{instruction}\n", title=title, border_style=DisplayTheme.SYSTEM))


def print_response(response, model_name=None):
    """Display AI response with syntax highlighting - unified system."""
    if not _display_controller.should_show_responses():
        return

    # Create title with model name if provided
    title = f"RESPONSE: {model_name}" if model_name else "RESPONSE"

    if isinstance(response, str):
        # Use Rich's JSON syntax highlighting (works even with malformed JSON)
        syntax = Syntax(response, "json", theme="monokai", line_numbers=False, word_wrap=True)
        console.print(Panel(syntax, title=title, border_style=DisplayTheme.RESPONSE))
    else:
        print_json(response, title)


def print_execution_header(sequence_id, models, user_prompt):
    """Display execution header with consistent Rich formatting - unified system."""
    console.print("\n" + "="*60, style=f"bold {DisplayTheme.INFO}")
    console.print("🚀 AI SYSTEMS - SEQUENCE EXECUTION", style=f"bold {DisplayTheme.INFO}", justify="center")
    console.print("="*60, style=f"bold {DisplayTheme.INFO}")

    # Show execution parameters
    exec_params = {
        "Sequence": sequence_id,
        "Models": ", ".join(models),
        "Prompt": user_prompt[:100] + "..." if len(user_prompt) > 100 else user_prompt
    }
    print_params(exec_params, "Execution Parameters")


def print_step_separator(step_num, total_steps):
    """Print a visual separator between steps - unified system."""
    console.print(f"\n{'─' * 20} STEP {step_num}/{total_steps} {'─' * 20}", style=DisplayTheme.SEPARATOR)

# =============================================================================
# ENHANCED UNIFIED FUNCTIONS
# =============================================================================

def print_system_status(message: str, success: bool = True, force_show: bool = False):
    """Print system status messages with unified formatting."""
    UnifiedDisplay.system_message(message, "success" if success else "error", force_show)

def print_file_operation(operation: str, path: str, success: bool = True, force_show: bool = False):
    """Print file operation messages with unified formatting."""
    UnifiedDisplay.file_operation(operation, path, success, force_show)

# =============================================================================
# DISPLAY CONTROLLER VALIDATION
# =============================================================================

def validate_display_settings():
    """Validate that display controller settings are properly configured."""
    controller = get_display_controller()
    settings = {
        "show_inputs": controller.should_show_inputs(),
        "show_system_instructions": controller.should_show_system_instructions(),
        "show_responses": controller.should_show_responses()
    }

    # Only show validation if at least one display type is enabled
    if any(settings.values()):
        print_system_status("Display settings validated", True, force_show=True)
        for setting, enabled in settings.items():
            status = "✓" if enabled else "✗"
            print_system_status(f"  {status} {setting.replace('_', ' ').title()}", enabled, force_show=True)
