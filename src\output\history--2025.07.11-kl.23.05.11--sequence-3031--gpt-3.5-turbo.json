  {
  "initial_prompt": "test cleanup",
  "sequence_id": "3031",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "test cleanup",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n  \"role\": \"explosive_decomposer\",\n  \"input\": [\n    \"test cleanup\"\n  ],\n  \"process\": [\n    \"identify_explicit_asks()\",\n    \"surface_hidden_assumptions()\",\n    \"enumerate_sub_goals()\",\n    \"reveal_blockers()\"\n  ],\n  \"constraints\": [\n    \"no_solutions()\",\n    \"pure_decomposition_only()\"\n  ],\n  \"requirements\": [\n    \"complete_goal_map()\"\n  ],\n  \"output\": {\n    \"goal_map\": [\n      \"Identify the type of test being referred to (software test, experiment, etc.)\",\n      \"Define what 'cleanup' entails in the context of the test\",\n      \"Determine the reason or objective behind the test cleanup\",\n      \"Check for any specific criteria or standards that need to be met during the cleanup process\",\n      \"Consider if there are any tools or methods required for the cleanup\",\n      \"Ensure that the cleanup process does not affect the integrity of the test results\"\n    ]\n  }\n}",
        }
      }
    }