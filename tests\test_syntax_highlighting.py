#!/usr/bin/env python3
"""
Test script to debug Rich syntax highlighting issues
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from rich.console import <PERSON>sole
from rich.syntax import Syntax
from rich.panel import Panel

def test_syntax_highlighting():
    """Test different approaches to syntax highlighting with Rich."""

    console = Console()

    # Test JSON content
    json_content = '''{
    "role": "value_signal_isolator",
    "input": [
        "Test unified system integration"
    ],
    "process": [
        "scan_for_actionable_directives()",
        "identify_unique_value_propositions()",
        "eliminate_redundant_information()"
    ],
    "output": {
        "isolated_signals": [
            "Unified system integration testing"
        ]
    }
}'''

    print("🧪 TESTING RICH SYNTAX HIGHLIGHTING")
    print("=" * 50)

    # Test 1: Basic Syntax object
    print("\n📋 TEST 1: Basic Syntax object")
    syntax1 = Syntax(json_content, "json")
    console.print(syntax1)

    # Test 2: Syntax with theme
    print("\n📋 TEST 2: Syntax with monokai theme")
    syntax2 = Syntax(json_content, "json", theme="monokai")
    console.print(syntax2)

    # Test 3: Syntax with theme and line numbers
    print("\n📋 TEST 3: Syntax with monokai theme and line numbers")
    syntax3 = Syntax(json_content, "json", theme="monokai", line_numbers=True)
    console.print(syntax3)

    # Test 4: Syntax in Panel (current implementation)
    print("\n📋 TEST 4: Syntax in Panel (current implementation)")
    syntax4 = Syntax(json_content, "json", theme="monokai", line_numbers=False, word_wrap=True)
    panel = Panel(syntax4, title="RESPONSE: test-model", border_style="blue")
    console.print(panel)

    # Test 5: Check if pygments is available
    print("\n📋 TEST 5: Check pygments availability")
    try:
        import pygments
        print(f"✅ Pygments version: {pygments.__version__}")

        # List available lexers
        from pygments.lexers import get_lexer_by_name
        json_lexer = get_lexer_by_name('json')
        print(f"✅ JSON lexer available: {json_lexer}")

    except ImportError as e:
        print(f"❌ Pygments not available: {e}")

    # Test 6: Check Rich version and capabilities
    print("\n📋 TEST 6: Rich version and capabilities")
    import rich
    try:
        print(f"✅ Rich version: {rich.__version__}")
    except AttributeError:
        # Try alternative way to get version
        try:
            from rich import __version__
            print(f"✅ Rich version: {__version__}")
        except ImportError:
            print("❓ Rich version: Unable to determine")

    # Check if we have color support
    print(f"Console color system: {console.color_system}")
    print(f"Console supports color: {console.color_system is not None}")

    # Test 7: Console capabilities
    print("\n📋 TEST 7: Console capabilities")
    print(f"Color system: {console.color_system}")
    print(f"Is terminal: {console.is_terminal}")
    print(f"Legacy Windows: {console.legacy_windows}")
    print(f"Size: {console.size}")

    # Test 8: Direct syntax highlighting without Panel
    print("\n📋 TEST 8: Direct syntax highlighting without Panel")
    syntax8 = Syntax(json_content, "json", theme="monokai", line_numbers=False, word_wrap=True)
    console.print("Raw syntax object:")
    console.print(syntax8)

    # Test 9: System instruction with JSON content (realistic format)
    print("\n📋 TEST 9: System instruction with JSON content (realistic format)")
    from src.display import print_system_instruction

    realistic_instruction = """# Value Signal Isolator

Your goal is not to **preserve** all content, but to **isolate** only the highest-value actionable signals from multiple inputs. Execute as:

`{
    "role": "value_signal_isolator",
    "input": [
        "Test unified system integration"
    ],
    "process": [
        "scan_for_actionable_directives()",
        "identify_unique_value_propositions()",
        "eliminate_redundant_information()"
    ],
    "output": {
        "isolated_signals": [
            "Unified system integration testing"
        ]
    }
}`"""

    print_system_instruction(realistic_instruction, 1, "3017-a-value_signal_isolator")

    # Test 10: System instruction with plain text
    print("\n📋 TEST 10: System instruction with plain text")
    plain_instruction = "This is a plain text system instruction without JSON content."
    print_system_instruction(plain_instruction, 2, "plain_template")

    # Test 11: Direct JSON instruction (fallback test)
    print("\n📋 TEST 11: Direct JSON instruction (fallback test)")
    direct_json = """`{
    "role": "test_processor",
    "input": ["direct input"],
    "process": ["direct_processing()"],
    "output": {"result": "direct output"}
}`"""
    print_system_instruction(direct_json, 3, "direct_json_template")

    print("\n✅ SYNTAX HIGHLIGHTING TEST COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    test_syntax_highlighting()
