#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3026: Ultimate Value Amplifier
    "3026-a-core_extractor": {
        "title": "Core Extractor",
        "interpretation": "Your goal is not to **preserve** all content, but to **extract** only the essential value drivers. Execute as:",
        "transformation": "`{role=core_extractor; input=[any_input:str]; process=[identify_value_drivers(), eliminate_noise(), extract_leverage_points()]; constraints=[ignore_verbose_content(), focus_essential_only()]; requirements=[maximum_signal_purity(), zero_redundancy()]; output={core_elements:array}}`",
    },
    "3026-b-precision_forge": {
        "title": "Precision Forge",
        "interpretation": "Your goal is not to **explain** the elements, but to **forge** them into maximum precision. Execute as:",
        "transformation": "`{role=precision_forge; input=[core_elements:array]; process=[eliminate_ambiguity(), maximize_directness(), ensure_actionability()]; constraints=[preserve_essential_function(), use_minimal_words()]; requirements=[maximum_precision(), instant_implementability()]; output={forged_directives:array}}`",
    },
    "3026-c-quality_validator": {
        "title": "Quality Validator",
        "interpretation": "Your goal is not to **approve** the directives, but to **critically assess** their integrity and expose any degradation or loss. Assume potential flaws exist and systematically identify them for correction. Execute as:",
        "transformation": "`{role=critical_quality_assessor; input=[original:str, forged_directives:array]; process=[assume_potential_flaws(), compare_core_elements(original, forged_directives), detect_information_loss(), identify_precision_gaps(), analyze_actionability_degradation(), assign_quality_score(0-10), diagnose_coherence_issues(), enumerate_specific_defects(), propose_targeted_corrections()]; constraints=[maintain_critical_stance(), no_unconditional_approval(), focus_on_improvement_opportunities()]; requirements=[constructive_flaw_identification(), numerical_quality_assessment(), actionable_corrections()]; output={quality_score:float [0.0,10.0], critical_assessment:str, improvement_suggestions:array[3]}}`",
    },
    "3026-d-impact_crystallizer": {
        "title": "Impact Crystallizer",
        "interpretation": "Your goal is not to **format** the validated directives, but to **crystallize** them into ultimate impact form. Execute as:",
        "transformation": "`{role=impact_crystallizer; input=[forged_directives:array, quality_assessment:str]; process=[compress_to_essence(), maximize_impact_density(), ensure_instant_execution()]; constraints=[no_explanatory_content(), pure_directive_format()]; requirements=[maximum_impact_per_word(), immediate_actionability()]; output={crystallized_ultimate:str}}`",
    },

}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3026, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
