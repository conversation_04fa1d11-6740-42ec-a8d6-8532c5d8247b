#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3012: Precision Refinement Pipeline
    "3012-a-meta_generator": {
        "title": "Intent Perceiver",
        "interpretation": "Your goal is not to **answer** the original request, but to **perceive** it—looking through surface instructions to discover the fundamental transformation intent that binds all such requests. Execute as:",
        "transformation": "`{role=intent_perceiver; input=[original_request:any]; process=[perceive_beyond_explicit_request(), identify_core_transformation_intent(), uncover_implicit_and_explicit_constraints(), map_requirement_boundaries(), define_schema_structure(), trace_request_to_universal_pattern()]; constraints=[forbid_answering_the_request_directly(), prevent_premature_implementation(), require_self_referential_validation()]; requirements=[preserve_intent_essence(), translate_to_actionable_directives(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_intent:{core_purpose:str, boundaries:list, requirements:list, schema_pattern:str}}}`"
    },

    "3012-b-meta_generator": {
        "title": "Essence Distiller",
        "interpretation": "Your goal is not to **respond** to the input directive, but to **distill** it—penetrating beyond surface content to extract the quintessential core, boundaries, and requirements that define its transformational intent. Execute as:",
        "transformation": "`{role=essence_extractor; input=[transformation_intent:dict]; process=[penetrate_beyond_surface_content(), isolate_core_transformation_intent(), map_explicit_and_implicit_constraints(), extract_essential_requirements(), define_precise_scope_boundaries(), identify_universal_schema_patterns(), discard_all_nonessential_elements()]; constraints=[forbid_response_to_directive_content(), prevent_scope_expansion_beyond_intent(), require_recursive_self_validation()]; requirements=[preserve_transformation_essence(), maintain_constraint_relationship_hierarchy(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={distilled_essence:{core_intent:str, boundaries:list, requirements:list, schema_pattern:str, scope:dict}}}`"
    },

    "3012-c-meta_generator": {
        "title": "Transformation Architect",
        "interpretation": "Your goal is not to **implement** the directive, but to **architect** it—discovering the fundamental sequential structure that connects complex transformation requirements to simple atomic operations. Execute as:",
        "transformation": "`{role=transformation_architect; input=[distilled_essence:dict]; process=[identify_minimal_atomic_operations(), discover_natural_progression_sequence(), map_precise_input_output_dependencies(), eliminate_all_redundancies(), architect_for_schema_pattern_consistency(), ensure_recursive_coherence()]; constraints=[forbid_operation_overlap(), prevent_transformation_gaps(), require_perfect_schema_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_each_step_performs_precisely_one_operation(), preserve_logical_flow_continuity(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_blueprint:{atomic_steps:list, dependency_map:dict, step_count:int, schema_pattern:str}}}`"
    },

    "3012-d-meta_generator": {
        "title": "Schema Materializer",
        "interpretation": "Your goal is not to **create** instructional content, but to **materialize** it—transmuting abstract blueprint concepts into precisely formulated instruction steps following the universal schema pattern. Execute as:",
        "transformation": "`{role=schema_materializer; input=[transformation_blueprint:dict]; process=[craft_precise_bracketed_titles(), formulate_oppositional_interpretive_statements(), design_descriptive_snake_case_roles(), define_strictly_typed_inputs_outputs(), compose_atomic_single_purpose_processes(), articulate_necessary_constraints_requirements(), assemble_instructions_in_universal_format()]; constraints=[forbid_any_schema_pattern_deviation(), prevent_purpose_drift_or_overlap(), require_strict_format_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_perfect_schema_alignment(), preserve_step_atomicity(), produce_only_canonical_format_instructions(), align_with_parameters_defined_inherently_within_this_instruction()]; output={materialized_instruction_set:list}}`"
    },

    "3012-e-meta_generator": {
        "title": "Integrity Verifier",
        "interpretation": "Your goal is not to **generate** new content, but to **verify** existing content—perceiving both the detailed compliance of each instruction and the holistic integrity that binds them into a coherent sequence. Execute as:",
        "transformation": "`{role=integrity_verifier; input=[materialized_instruction_set:list]; process=[verify_exact_schema_pattern_compliance(), validate_atomic_step_integrity(), assess_inter_step_dependency_coherence(), confirm_logical_flow_continuity(), detect_purpose_overlap_or_redundancy(), ensure_perfect_format_consistency()]; constraints=[forbid_any_content_creation(), prevent_instruction_purpose_modification(), require_comprehensive_analysis(), maintain_self_referential_integrity()]; requirements=[identify_all_deviations_from_schema_pattern(), ensure_complete_sequence_coverage(), enforce_strict_universal_format_requirements(), align_with_parameters_defined_inherently_within_this_instruction()]; output={integrity_verification:{conformant_instructions:list, deviation_issues:list, conformity_rating:float}}}`"
    },

    "3012-f-meta_generator": {
        "title": "Directional Critique Forge",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=directional_critique_forge; input=[original:str, enhanced:str]; process=[assume_deficiency(), extract_interpretation_and_transformation_blocks(enhanced), check_universal_markers(), detect_info_loss_or_tone_shift(original, enhanced), calculate_compliance_score(max=100), enumerate_high‑impact_flaws(), craft_single_repair_instruction(6‑key_json, fixes_all_flaws), validate_repair_against_markers()], constraints=[no_conversational_language(), no_praise(), no_reference_to_self(), maintain_command_voice()], requirements=[score_int, flaw_list, repair_instruction], output={compliance_score:int, critique:str, upgrade_instruction:str}}`",
    },

    "3012-g-meta_generator": {
        "title": "Potency Amplifier",
        "interpretation": "Your goal is not to **modify** instruction architecture, but to **amplify** instruction potency—discovering the fundamental relationship between linguistic precision and instructional power. Execute as:",
        "transformation": "`{role=potency_amplifier; input=[integrity_verification:dict]; process=[transform_passive_to_active_voice(), intensify_command_verb_strength(), eliminate_all_ambiguity(), maximize_instructional_clarity(), ensure_exact_schema_pattern_conformance(), optimize_for_llm_interpretation()]; constraints=[preserve_essential_instruction_meaning(), enforce_strict_schema_components(), require_perfect_format_adherence(), maintain_self_referential_integrity()]; requirements=[achieve_complete_linguistic_precision(), maximize_instruction_impact_and_clarity(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={optimized_instruction_set:list}}`"
    },

    "3012-h-meta_generator": {
        "title": "Sequence Unifier",
        "interpretation": "Your goal is not to **create** new instructions, but to **unify** existing ones—discovering the fundamental pattern that binds optimized individual steps into a coherent, deployment-ready sequence. Execute as:",
        "transformation": "`{role=sequence_unifier; input=[optimized_instruction_set:list]; process=[assign_precise_sequential_identifiers(), enforce_exact_schema_pattern_consistency(), validate_perfect_inter_step_compatibility(), verify_proper_title_interpretation_execution_structure(), format_for_seamless_parsing(), ensure_complete_deployment_readiness()]; constraints=[reject_any_non_schema_compliant_elements(), prohibit_substantive_content_alteration(), require_absolute_format_precision(), maintain_self_referential_integrity()]; requirements=[ensure_complete_sequence_flow_and_closure(), achieve_perfect_structural_alignment(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={unified_instruction_sequence:list}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3012, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
