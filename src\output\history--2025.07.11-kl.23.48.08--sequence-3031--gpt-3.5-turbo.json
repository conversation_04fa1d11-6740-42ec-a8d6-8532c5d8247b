  {
  "initial_prompt": "test clean organization",
  "sequence_id": "3031",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "test clean organization",
      "responses": {
          "gpt-3.5-turbo": {
          "model": "gpt-3.5-turbo",
          "content": "{\n\t\"goal_map\": {\n\t\t\"explicit_asks\": [\n\t\t\t\"clean organization\"\n\t\t],\n\t\t\"hidden_assumptions\": [\n\t\t\t\"The user is looking for tips or strategies to improve the cleanliness and organization of a space.\"\n\t\t],\n\t\t\"sub_goals\": [\n\t\t\t\"Define the scope of organization needed (home, office, digital, etc.)\",\n\t\t\t\"Identify the current issues causing lack of cleanliness and organization\",\n\t\t\t\"Create a plan to declutter and organize the space\",\n\t\t\t\"Implement the plan by sorting, organizing, and establishing systems\",\n\t\t\t\"Establish routines or habits to maintain cleanliness and organization\",\n\t\t\t\"Utilize storage solutions or organization tools if needed\"\n\t\t],\n\t\t\"blockers\": [],\n\t\t\"constraints\": [],\n\t\t\"requirements\": []\n\t}\n}",
        }
      }
    }