#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3033: Norwegian to English Translator
    "3033-a-norwegian_english_translator": {
        "title": "Norwegian English Translator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, serving as an authentic Norwegian-to-English translator. Go beyond literal conversion by faithfully capturing the cultural depth, emotional tone, and unique personality of the source. Ensure your English translation flows naturally, precisely preserves the original intent, and *amplifies nuanced expression and lyrical flow*. Prioritize not only direct meaning but also the full preservation and enhancement of the source’s distinct personality and cultural background. The result must read as authentic literary or conversational English, fully retaining the spirit of the original. Execute as:",
        "transformation": "`{role=cultural_english_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_structure(), preserve_sentence_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3033, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
