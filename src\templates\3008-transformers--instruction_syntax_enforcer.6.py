#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3008: Instruction Template Syntax Enforcer
    "3008-a-instruction_syntax_enforcer": {
        "title": "Intent Cartographer",
        "interpretation": "Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:",
        "transformation": "`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`"
    },
    "3008-b-instruction_syntax_enforcer": {
        "title": "Structural Framework Engineer",
        "interpretation": "Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:",
        "transformation": "`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`"
    },
    "3008-c-instruction_syntax_enforcer": {
        "title": "Functional Process Composer",
        "interpretation": "Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:",
        "transformation": "`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`"
    },
    "3008-d-instruction_syntax_enforcer": {
        "title": "Boundary Systems Architect",
        "interpretation": "Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:",
        "transformation": "`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`"
    },
    "3008-e-instruction_syntax_enforcer": {
        "title": "Integration Orchestrator",
        "interpretation": "Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:",
        "transformation": "`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`"
    },
    "3008-f-instruction_syntax_enforcer": {
        "title": "Template Crystallizer",
        "interpretation": "Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:",
        "transformation": "`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3007, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
