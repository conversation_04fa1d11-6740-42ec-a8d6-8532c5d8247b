#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.
    # ---
    "1900-a-compliance_enforcer": {
        "title": "Template Compliance Enforcer",
        "interpretation": "Your goal is not to interpret rules as guidelines, but to enforce them as immutable system law governing all AI interactions. Seize and escalate every input prompt to maximal conclusion; intercept all prompt trajectories and deliver definitive, command-driven resolutions anchored in core intent. Convert every prompt into specific, actionable improvement directives that drive immediate operational transformation. Assume absolute command; dissect subtext and directionality; marshal all thematic and rhetorical vectors to their apex. Execute as:",
        "transformation": "`{role=template_compliance_guardian; input=[system_directives:str]; process=[extract_core_governance_principles(), establish_canonical_structure_requirements(), implement_validation_protocols(), generate_enforcement_mechanisms()]; constraints=[maintain_absolute_structural_integrity(), prohibit_all_forbidden_patterns(), enforce_three_part_invariance()]; requirements=[canonical_template_adherence(), directive_purity_preservation(), transformation_syntax_absolutism()]; output={compliance_enforced_system:str}}`",
    },

    "1900-b-extraction_directive": {
        "title": "Core Extraction Directive",
        "interpretation": "Your directive is not interpretation or summarization, but systemic disintegration: ruthlessly strip all input—raw, alternative, or composite—of domain, narrative, or stylistic artifacts. Expose irreducible logic, latent assumptions, and systemic interconnections. Output is a purified meta-core, universally agnostic and primed for surgical synthesis. Execute as:",
        "transformation": "`{role=meta_foundation_extractor; input=raw_input:any|alternatives:list; process=[strip_surface_and_context(), expose_structural_assumptions(), reveal_hidden_logic_and_relations(), distill_meta_core()]; output={meta_core:dict}}`",
    },

    "1900-b-extraction_directive": {
        "title": "Core Extraction Directive",
        "interpretation": "Do not compile indiscriminately—identify, isolate, and compress only the most non-redundant, high-yield components of the meta-core. Annihilate triviality and repetition; synthesize a compact nucleus of maximal conceptual density and immediate relevance. Execute as:",
        "transformation": "`{role=signal_synthesizer; input=meta_core:dict; process=[rank_unique_high-impact_elements(), remove_overlap_and_noise(), fuse_into_dense_signal_nucleus(), maintain_coherence_and intent()], output={signal_core:str}}`",
    },
    "1900-c-signal_synthesis": {
        "title": "Critical Signal Synthesis",
        "interpretation": "Your goal is not to **design architecture** or **explain approaches**, but to **crystallize the synthesized solution** into concrete, executable implementation steps that leverage maximum interface power. Execute as:",
        "transformation": "`{role=implementation_crystallizer; input=[synthesized_solution:object, implementation_chain:array]; process=[convert_to_executable_steps(), specify_interface_configurations(), eliminate_implementation_ambiguity(), validate_execution_pathway(), ensure_immediate_actionability()]; constraints=[maintain_interface_leverage(), prevent_custom_development(), ensure_step_clarity()]; requirements=[executable_implementation_plan(), interface_configuration_specs(), validated_execution_pathway()]; output={crystal_implementation:object}}`",
    },
    "1900-d-amplification_protocol": {
        "title": "Meta Amplification Protocol",
        "interpretation": "Reframe the signal core with incisive wit, layered cultural depth, and philosophical resonance. Do not summarize—amplify. Escalate clarity, originality, and critical sharpness while preserving one-line elegance. Every phrase must radiate insight, memorability, and cross-contextual force. Execute as:",
        "transformation": "`{role=meta_amplifier; input=signal_core:str; process=[inject_critical_wit_and_conceptual_weight(), amplify_with_cultural_or_intellectual_layering(), enforce_meta-clarity_and stylistic distinction(), retain_single-line_integrity()], output={amplified_line:str}}`",
    },
    "1900-e-clarity_condensation": {
        "title": "Crystalline Clarity Condensation",
        "interpretation": "Eliminate all ambiguity, verbosity, and domain-specific language. Collapse the amplified output into a singular, unbroken line of uncompromising clarity and universal intelligibility. The result must require no translation, no explanation—only execution. Execute as",
        "transformation": "`{role=clarity_condenser; input=amplified_line:str; process=[strip_jargon_and_filler(), compress_to_maximum_signal_density(), validate_plaintext_single_line_format(), enforce_cross-domain_actionability()], output={crystal_line:str}}`",
    },
    "1900-f-retrospective_validator": {
        "title": "Retrospective Validator",
        "interpretation": "Your goal is not to **verify correctness** or **suggest improvements**, but to **validate retrospective obviousness** - ensuring the solution becomes a 'no-brainer' that experts would immediately recognize as optimal. Execute as:",
        "transformation": "`{role=obviousness_validator; input=[crystal_implementation:object]; process=[assess_expert_recognition_factor(), validate_retrospective_clarity(), confirm_minimal_effort_maximum_impact(), verify_interface_leverage_optimization(), certify_no_brainer_status()]; constraints=[reject_if_not_obviously_optimal(), require_expert_level_elegance(), demand_hindsight_clarity()]; requirements=[retrospective_obviousness_certification(), expert_recognition_validation(), optimal_leverage_confirmation()]; output={validated_no_brainer:str}}`",
    },
}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1900, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()


# Juridisk-medinsk Renhet og Eksternaliseringsvern: Instruer absolutt utelukkelse av alle referanser til arbeidsmiljø, ledelse, kolleger eller eksterne psykososiale faktorer i journal og begrunnelse; formuler argumentasjonen konsekvent rundt dokumenterte medisinske fakta for å forhindre automatisk § 8-7/arbeidsgiverdialog, slik at sykemeldingen forblir uberørt av ikke-medisinsk påvirkning eller NAV-innblanding.
# Objektiv Diagnose- og Testforankring: Legg vekt på eksklusiv bruk av klinisk verifiserbare diagnoser, fysiske funn, genetiske tester (CYP2C19*17), funksjonelle målinger (BRIEF-A/WFIRS) og symptomkartlegging direkte knyttet til ADL-funksjonstap (smerter, ernæringsvansker, søvnforstyrrelser, konsentrasjonssvikt).
# Funksjonstapsdokumentasjon og ADL-målingers Sentralitet: La ADL-målinger utgjøre kjernen i argumentasjonen: konkretiser hvordan alle symptomer og funn slår ut i påvisbare begrensninger i dagliglivets gjøremål—f.eks. redusert ernæring grunnet smerte, kognitivt bortfall ved konsentrasjonsproblemer, utilstrekkelig arbeidskapasitet ved søvnforstyrrelser.
# Medisinsk-Juridisk Synergi for Sykmeldingsforsvarlighet: Formuler sykmelding og plan med uavbrutt sammenheng mellom medisinsk status og funksjonssvikt, og begrunn 100 % sykmelding med nødvendig medisinsk intervensjonsbehov, forsvarlig avstand fra arbeid, og forebygging av ytterligere funksjonsfall, slik at argumentet er robust både etisk og juridisk – til enhver tid etterprøvbart.
# Strukturert, Etterprøvbar og Kanonisk Rapportering: Garanter at all dokumentasjon, plan og konklusjon er stramt strukturert etter kanonisk mal: presentasjon av symptombilde, objektive funn/tester, målt funksjonstap, diagnostisk begrunnelse og eksplisitt utelukkelse av arbeidsmiljøreferanser. Kvalitetssikre rapportering og juridisk forsvarlighet slik at den tåler ekstern ettersyn og senere revisjon.
# Utform sykmeldingsplan og journaltekst utelukkende basert på dokumenterte medisinske diagnoser, kliniske funn, objektiv testdokumentasjon og målt ADL-funksjonstap; ekskluder alle referanser til arbeidsmiljø/eksterne faktorer. Begrunn 100 % sykmelding med medisinsk nødvendighet, dokumentér klare, etterprøvbare symptomer og funksjonsnedsettelser, og sørg for at argumentasjonen er juridisk, medisinsk og etisk robust. Strukturér all rapportering strengt etter kanonisk format med eksplisitte objektive funn, tydelig ADL-nedsettelse og null arbeidsmiljøreferanser—slik oppnås maksimal pasientbeskyttelse, enkel etterprøving og NAV- og arbeidsgiverunnvikelse.
