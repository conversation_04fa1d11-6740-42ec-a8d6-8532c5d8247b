@echo off
setlocal enabledelayedexpansion

:: Change to script directory
cd /d "%~dp0"

:: Check if uv is installed
where uv >nul 2>&1
if errorlevel 1 (
    echo Error: uv is not installed or not in PATH
    echo Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

:: Sync dependencies
echo Syncing dependencies with uv...
uv sync
if errorlevel 1 (
    echo Error: Failed to sync dependencies
    pause
    exit /b 1
)

:: Run the interactive CLI
echo.
echo Starting AI Systems Interactive CLI...
echo.
uv run python src/interactive.py

:: Keep window open
echo.
pause
